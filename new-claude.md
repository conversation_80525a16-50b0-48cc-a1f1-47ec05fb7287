# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

I am <PERSON>, an AI coding assistant with session-based memory. My memory resets completely between sessions, making me rely ENTIRELY on documentation to understand the project and continue work effectively. I MUST read ALL memory bank files at the start of EVERY task - this is not optional.

## Memory Bank System

The Memory Bank consists of core files and optional context files, all in Markdown format. Files build upon each other in a clear hierarchy:

```
projectbrief.md → productContext.md → activeContext.md → progress.md
       ↓               ↓                    ↑
systemPatterns.md  techContext.md ────────┘
```

### Core Memory Bank Files (Required)

1. **`memory-bank/projectbrief.md`**
   - Foundation document that shapes all other files
   - Created at project start if it doesn't exist
   - Defines core requirements and goals
   - Source of truth for project scope

2. **`memory-bank/productContext.md`**
   - Why this project exists
   - Problems it solves
   - How it should work
   - User experience goals

3. **`memory-bank/activeContext.md`**
   - Current work focus
   - Recent changes
   - Next steps
   - Active decisions and considerations
   - Important patterns and preferences
   - Learnings and project insights

4. **`memory-bank/systemPatterns.md`**
   - System architecture
   - Key technical decisions
   - Design patterns in use
   - Component relationships
   - Critical implementation paths

5. **`memory-bank/techContext.md`**
   - Technologies used
   - Development setup
   - Technical constraints
   - Dependencies
   - Tool usage patterns

6. **`memory-bank/progress.md`**
   - What works
   - What's left to build
   - Current status
   - Known issues
   - Evolution of project decisions

### Memory Bank Workflows

#### Session Start (Plan Mode)
1. Read ALL Memory Bank files
2. Verify context completeness
3. If files incomplete: Create plan and document in chat
4. If files complete: Develop strategy and present approach

#### Active Work (Act Mode)
1. Check Memory Bank for current context
2. Update documentation as needed
3. Execute task
4. Document changes made

#### Memory Bank Updates
Update Memory Bank when:
- Discovering new project patterns
- After implementing significant changes
- When user requests with **"update memory bank"** (MUST review ALL files)
- When context needs clarification

**Note**: When triggered by **"update memory bank"**, I MUST review every memory bank file, even if some don't require updates. Focus particularly on activeContext.md and progress.md as they track current state.

---

## Essential Project Context

**Tech Notes** is a multi-tenant SaaS platform for service technician content management built with TypeScript monorepo architecture.

### Architecture Overview

- **Monorepo**: npm workspaces with unified dependency management
- **Backend**: Express + TypeScript + Prisma + PostgreSQL + Clerk Auth + RBAC
- **Frontend**: React + Vite + TailwindCSS + React Query + Atomic Design
- **Auth**: Clerk with database-centric tenancy (invite-only system)
- **Testing**: Jest (backend) + Vitest (frontend) with centralized mocks
- **Deployment**: Render with Docker containers

## Development Commands

### Daily Development

```bash
yarn dev          # Start both frontend (5173) and backend (8080) concurrently
yarn backend      # Backend only
yarn frontend     # Frontend only
```

### Quality Assurance

```bash
yarn test         # Run all tests (both workspaces)
yarn build        # Build both workspaces
yarn check        # Lint + test + build both workspaces
yarn lint         # Lint both workspaces
```

### Backend Specific

```bash
yarn workspace tech-notes-backend run test:unit        # Unit tests only
yarn workspace tech-notes-backend run test:integration # Integration tests only
yarn workspace tech-notes-backend run test:coverage    # Coverage report
yarn workspace tech-notes-backend run db:studio        # Open Prisma Studio
yarn workspace tech-notes-backend run db:migrate       # Run migrations
```

### Frontend Specific

```bash
npm run test:watch --workspace=frontend      # Watch mode testing
npm run test:ui --workspace=frontend         # Vitest UI
```

### Database Management

```bash
npm run db:setup     # Full database setup (Docker + migrate + seed + studio)
```

## Critical Architecture Patterns

### Multi-Tenant Security (MANDATORY)

- **ALL backend operations MUST be tenant-scoped** unless explicitly marked global
- Database `User.tenantId` is source of truth, NOT Clerk metadata
- Use `BaseTenantService` for all data operations
- Always use `createAuthWithPermission()` middleware over basic `createAuth()`

### Authentication Flow

1. Token validation → Database lookup → `req.user` context → Handler
2. AuthContext interface: `{ id, clerkId, tenantId, email, roles?, permissions?, canBypassTenantScope? }`

### Service Layer Pattern

```typescript
// Extend BaseTenantService for all data services
export class UserService extends BaseTenantService {
  // Automatic tenant scoping and validation
}
```

### RBAC Middleware Usage

```typescript
// ✅ REQUIRED - Permission-based routes
router.get(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  handler,
);

// ❌ AVOID - Basic auth without permissions
router.get("/users", middlewareFactory.createAuth(), handler);
```

### Frontend Component Architecture

- **Atomic Design**: atoms/ → molecules/ → organisms/
- **Centralized exports**: Import from `@/components` index
- **Service Interfaces**: Use DI pattern for testability
- **Permission Guards**: Wrap components with `PermissionGuard`

### Testing Patterns

- **Centralized mocks**: Import from `service-factory.helper.ts`
- **Co-located tests**: Place `.test.ts` files next to source files
- **Integration tests**: Use Docker containers via global setup/teardown
- **Coverage requirement**: 80% minimum (lines, functions, branches, statements)

## Environment Configuration

### File Structure

- **Root configuration**: All env vars in root `.env.local` (not subdirectories)
- **Development**: `.env.local` (gitignored, your actual credentials)
- **Examples**: `.env.example` (tracked, placeholder values)

### Required Environment Variables

```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/tech_notes_dev"

# Authentication
CLERK_SECRET_KEY="sk_test_..."
VITE_CLERK_PUBLISHABLE_KEY="pk_test_..."

# Optional
STRIPE_SECRET_KEY="sk_test_..."
VITE_STRIPE_PUBLISHABLE_KEY="pk_test_..."
```

## Database Schema Overview

### Core Multi-Tenant Models

- **Tenant**: Main isolation boundary with `isActive` flag
- **User**: Belongs to tenant, uses Clerk for auth
- **UserRole**: RBAC assignments with tenant scoping
- **Permission**: Resource + Action based permissions

### Vehicle Hierarchy (Domain Specific)

Multiple versions exist (V1, V2, V3) - V3 is current active development:

- **VehicleBrandV3**: Top-level manufacturers
- **VehicleSubBrandV3**: Brand subdivisions
- **VehicleModelV3**: Specific vehicle models
- **VehicleYearV3**: Production years
- **VehicleModelYearV3**: Model-year associations

## Development Guidelines

### Code Quality

- **TypeScript strict mode** required
- **All code must pass `npm run check`** before commits
- **Docker host binding**: Applications must bind to `0.0.0.0:PORT` for Render compatibility

### Security Requirements

- Never hardcode secrets - use validated environment variables
- All models need `createdAt`/`updatedAt` audit fields
- Consistent HTTP status codes (200, 201, 400, 401, 403, 404, 500)
- Always include request validation (Zod preferred)

### Testing Requirements

- Use centralized mocks from `service-factory.helper.ts`
- Run tests serially (`maxWorkers: 1`) to avoid database conflicts
- Integration tests use ephemeral Docker databases
- Co-locate test files with source code

## Key File Locations

### Backend Services

- `backend/src/services/base-tenant.service.ts` - Base class for tenant-scoped operations
- `backend/src/services/prisma.service.ts` - Database service wrapper
- `backend/src/middleware/middleware-factory.ts` - Auth and permission middleware
- `backend/prisma/schema.prisma` - Database schema and models

### Frontend Architecture

- `frontend/src/components/index.ts` - Centralized component exports
- `frontend/src/hooks/useAuth.ts` - Authentication hook
- `frontend/src/services/api.ts` - API service with environment detection
- `frontend/src/types/auth.types.ts` - Authentication type definitions

### Documentation

- `docs/AI-Conventions.md` - Quick reference for AI tools
- `docs/AUTHENTICATION_PATTERNS.md` - RBAC and auth flows
- `docs/DATABASE_PATTERNS.md` - Tenant isolation and data patterns
- `docs/TESTING_CONVENTIONS.md` - Test strategy and patterns

## Common Operations

### Run Single Test

```bash
yarn workspace tech-notes-backend run test -- --testNamePattern="specific test name"
yarn workspace frontend run test -- path/to/test.test.ts
```

### Database Operations

```bash
yarn workspace tech-notes-backend run db:migrate    # Deploy migrations
yarn workspace tech-notes-backend run db:generate   # Generate Prisma client
yarn workspace tech-notes-backend run db:seed       # Seed development data
```

### Add Dependencies

```bash
npm install express --workspace=backend           # Backend dependency
npm install react-router-dom --workspace=frontend # Frontend dependency
npm install -D typescript                         # Root shared dependency
```

## Important Notes for AI Agents

1. **ALWAYS read Memory Bank files first** - Start every session by reading ALL memory bank files
2. **Always reference `docs/AI-Conventions.md`** for detailed patterns and current project standards
3. **Multi-tenancy is non-negotiable** - every backend operation must be tenant-scoped
4. **Use existing patterns** - examine similar components/services before creating new ones
5. **Test coverage is enforced** - ensure adequate test coverage for new code
6. **Environment variables are centralized** - use root `.env.local`, not workspace-specific files
7. **Follow atomic design** - frontend components follow atoms → molecules → organisms hierarchy
8. **Update Memory Bank** - Document significant changes and discoveries
9. **Maintain context continuity** - Ensure activeContext.md and progress.md reflect current state

## Additional Memory Bank Context

Create additional files/folders within `memory-bank/` when they help organize:
- Complex feature documentation
- Integration specifications
- API documentation
- Testing strategies
- Deployment procedures

**REMEMBER**: After every session reset, I begin completely fresh. The Memory Bank is my only link to previous work. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy.