import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON>,
  CardHeader,
  CardContent,
  <PERSON><PERSON>,
  Alert,
  LoadingSpinner,
  Badge,
  Modal,
  PermissionGuard,
  SystemAdminOnly,
  CompanyAdminOnly,
  UserManagementAccess,
  Input,
  FormField,
} from "../../components";
import {
  useTypedApi,
  type User,
  type UsersResponse,
  type UpdateUserData,
} from "../../services/api-client";
import { usePermissions } from "../../hooks/usePermissions";
import { Users, Shield, Mail } from "lucide-react";

export const UsersPage: React.FC = () => {
  const api = useTypedApi();
  const queryClient = useQueryClient();
  const { isSystemAdmin, canViewUsers } = usePermissions();

  // Modal state for user details
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUserDetailModalOpen, setIsUserDetailModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isEditUserModalOpen, setIsEditUserModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isBulkActionsModalOpen, setIsBulkActionsModalOpen] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(
    new Set(),
  );

  // Fetch users
  const {
    data: usersResponse,
    isLoading,
    error,
    refetch,
  } = useQuery<UsersResponse>({
    queryKey: ["users"],
    queryFn: () => api.users.getAll(),
    enabled: canViewUsers,
  });

  const users = usersResponse?.data || [];

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({
      userId,
      updateData,
    }: {
      userId: string;
      updateData: UpdateUserData;
    }) => api.users.update(userId, updateData),
    onSuccess: () => {
      toast.success("User updated successfully");
      queryClient.invalidateQueries({ queryKey: ["users"] });
      handleCloseEditUser();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update user");
    },
  });

  // Helper function to get user display name
  const getUserDisplayName = (user: User) => {
    const fullName = `${user.firstName || ""} ${user.lastName || ""}`.trim();
    return fullName || user.email;
  };

  // Helper function to get role badge variant
  const getRoleBadgeVariant = (roleType: string) => {
    switch (roleType) {
      case "SYSTEM_ADMIN":
        return "admin" as const;
      case "COMPANY_ADMIN":
        return "user" as const;
      case "COMPANY_TECH":
        return "tech" as const;
      default:
        return "warning" as const;
    }
  };

  // Handle user actions
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsUserDetailModalOpen(true);
  };

  const handleAddUser = () => {
    setIsAddUserModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsEditUserModalOpen(true);
  };

  const handleCloseUserDetail = () => {
    setIsUserDetailModalOpen(false);
    setSelectedUser(null);
  };

  const handleCloseAddUser = () => {
    setIsAddUserModalOpen(false);
  };

  const handleCloseEditUser = () => {
    setIsEditUserModalOpen(false);
    setEditingUser(null);
  };

  const handleBulkActions = () => {
    setIsBulkActionsModalOpen(true);
  };

  const handleCloseBulkActions = () => {
    setIsBulkActionsModalOpen(false);
  };

  const handleSelectUser = (userId: string, selected: boolean) => {
    setSelectedUserIds((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(userId);
      } else {
        newSet.delete(userId);
      }
      return newSet;
    });
  };

  const handleSelectAllUsers = (selected: boolean) => {
    if (selected) {
      setSelectedUserIds(new Set(users.map((user) => user.id)));
    } else {
      setSelectedUserIds(new Set());
    }
  };

  const handleExportUsers = () => {
    try {
      // Create CSV content
      const headers = [
        "Name",
        "Email",
        "Status",
        "Tenant",
        "Roles",
        "Created Date",
      ];
      const csvContent = [
        headers.join(","),
        ...users.map((user) => {
          const name = getUserDisplayName(user);
          const status = user.isActive ? "Active" : "Inactive";
          const tenant = user.tenant
            ? `${user.tenant.name} (${user.tenant.slug})`
            : user.tenantId;
          const roles =
            user.userRoles?.map((ur) => ur.role.name).join("; ") || "No roles";
          const createdDate = new Date(user.createdAt).toLocaleDateString();

          // Escape commas and quotes in CSV
          const escapeCSV = (field: string) => {
            if (
              field.includes(",") ||
              field.includes('"') ||
              field.includes("\n")
            ) {
              return `"${field.replace(/"/g, '""')}"`;
            }
            return field;
          };

          return [
            escapeCSV(name),
            escapeCSV(user.email),
            escapeCSV(status),
            escapeCSV(tenant),
            escapeCSV(roles),
            escapeCSV(createdDate),
          ].join(",");
        }),
      ].join("\n");

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `users-export-${new Date().toISOString().split("T")[0]}.csv`,
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${users.length} users to CSV`);
    } catch (error) {
      console.error("Export failed:", error);
      toast.error("Failed to export users");
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (
      window.confirm(
        `Are you sure you want to delete ${getUserDisplayName(user)}?`,
      )
    ) {
      try {
        await api.users.delete(user.id);
        toast.success(`User ${getUserDisplayName(user)} deleted successfully`);
        refetch();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete user";
        toast.error(errorMessage);
        console.error("Failed to delete user:", error);
      }
    }
  };

  const handleToggleUserStatus = async (user: User) => {
    try {
      await api.users.toggleStatus(user.id);
      const newStatus = user.isActive ? "deactivated" : "activated";
      toast.success(
        `User ${getUserDisplayName(user)} ${newStatus} successfully`,
      );
      refetch();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to toggle user status";
      toast.error(errorMessage);
      console.error("Failed to toggle user status:", error);
    }
  };

  // Check if user has permission to view this page
  if (!canViewUsers) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Users</h1>
        <Alert variant="warning">
          You don't have permission to view users. Contact your administrator
          for access.
        </Alert>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Users</h1>
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Users</h1>
        <Alert variant="error">
          <p>Failed to load users. Please try again.</p>
          <Button onClick={() => refetch()} className="mt-2">
            Retry
          </Button>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-6 border border-blue-100/50">
        <div className="flex justify-between items-start">
          <div className="max-w-2xl">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              User Management
            </h1>
            <p className="mt-3 text-lg text-gray-600 leading-relaxed">
              Manage users in your organization
              {usersResponse?.meta.tenantId && !isSystemAdmin && (
                <span className="block text-sm text-gray-500 mt-1">
                  Tenant: {usersResponse.meta.tenantId}
                </span>
              )}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Invitations Button - For users who can manage users */}
            <UserManagementAccess>
              <Button
                variant="outline"
                onClick={() => window.location.href = "/app/invitations"}
                className="border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-300"
              >
                <Mail className="h-4 w-4 mr-2" />
                Invitations
              </Button>
            </UserManagementAccess>

            {/* Add User Button - Only for users who can manage users */}
            <PermissionGuard permission={{ resource: "USER", action: "WRITE" }}>
              <Button
                onClick={handleAddUser}
                className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 shadow-lg"
              >
                <Users className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </PermissionGuard>
          </div>
        </div>
      </div>

      {/* Enhanced Users Count */}
      <div className="bg-gradient-to-r from-white to-gray-50/50 p-6 rounded-xl border border-gray-200/60 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <span className="text-lg font-medium text-gray-900">
              Total Users
            </span>
          </div>
          <Badge
            variant="primary"
            className="text-lg px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600"
          >
            {usersResponse?.meta.count || 0}
          </Badge>
        </div>
      </div>

      {/* Enhanced Bulk Selection Header - Only for System Admins */}
      {isSystemAdmin && users.length > 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200/60">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={
                    selectedUserIds.size === users.length && users.length > 0
                  }
                  onChange={(e) => handleSelectAllUsers(e.target.checked)}
                  className="w-5 h-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-base font-medium text-gray-900">
                  {selectedUserIds.size === 0
                    ? "Select all users"
                    : `${selectedUserIds.size} user${selectedUserIds.size === 1 ? "" : "s"} selected`}
                </span>
              </div>
              {selectedUserIds.size > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkActions}
                  className="bg-white/80 border-purple-200 hover:bg-purple-50"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Bulk Actions ({selectedUserIds.size})
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Users List */}
      <div className="space-y-4">
        {users.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-gray-500">No users found.</p>
            </CardContent>
          </Card>
        ) : (
          users.map((user) => (
            <Card key={user.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      {/* Checkbox for System Admins */}
                      {isSystemAdmin && (
                        <input
                          type="checkbox"
                          checked={selectedUserIds.has(user.id)}
                          onChange={(e) =>
                            handleSelectUser(user.id, e.target.checked)
                          }
                          className="rounded border-gray-300"
                        />
                      )}
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {getUserDisplayName(user)}
                        </h3>
                        <p className="text-gray-600">{user.email}</p>
                        {/* Show tenant info for System Admins */}
                        {isSystemAdmin && user.tenant && (
                          <p className="text-sm text-blue-600 mt-1">
                            Tenant: {user.tenant.name} ({user.tenant.slug})
                          </p>
                        )}
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge
                            variant={user.isActive ? "default" : "warning"}
                          >
                            {user.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {user.userRoles?.map((userRole) => (
                            <Badge
                              key={userRole.id}
                              variant={getRoleBadgeVariant(userRole.role.type)}
                            >
                              {userRole.role.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2">
                    {/* View Details Button - Available to all users who can view users */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewUser(user)}
                    >
                      View Details
                    </Button>

                    {/* Edit Button - For users who can manage users */}
                    <PermissionGuard
                      permission={{ resource: "USER", action: "WRITE" }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditUser(user)}
                      >
                        Edit
                      </Button>
                    </PermissionGuard>

                    {/* Toggle Status - For Company Admins and above */}
                    <CompanyAdminOnly>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleUserStatus(user)}
                      >
                        {user.isActive ? "Deactivate" : "Activate"}
                      </Button>
                    </CompanyAdminOnly>

                    {/* Delete Button - For users who can delete users */}
                    <PermissionGuard
                      permission={{ resource: "USER", action: "DELETE" }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        Delete
                      </Button>
                    </PermissionGuard>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* System Admin Only Section */}
      <SystemAdminOnly>
        <Card>
          <CardHeader title="System Administration" />
          <CardContent>
            <p className="text-gray-600 mb-4">
              As a System Administrator, you can view and manage users across
              all tenants.
            </p>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleExportUsers}>
                Export Users
              </Button>
              <Button variant="outline" size="sm" onClick={handleBulkActions}>
                Bulk Actions
              </Button>
            </div>
          </CardContent>
        </Card>
      </SystemAdminOnly>

      {/* User Detail Modal */}
      <Modal
        isOpen={isUserDetailModalOpen}
        onClose={handleCloseUserDetail}
        title={
          selectedUser
            ? `User Details: ${getUserDisplayName(selectedUser)}`
            : "User Details"
        }
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Full Name
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {getUserDisplayName(selectedUser)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Email
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedUser.email}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    User ID
                  </label>
                  <code className="mt-1 text-xs bg-gray-100 px-2 py-1 rounded block">
                    {selectedUser.id}
                  </code>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Status
                  </label>
                  <div className="mt-1">
                    <Badge
                      variant={selectedUser.isActive ? "success" : "warning"}
                    >
                      {selectedUser.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Created
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedUser.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Last Updated
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedUser.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Roles and Permissions */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Roles & Permissions
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assigned Roles
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {selectedUser.userRoles &&
                    selectedUser.userRoles.length > 0 ? (
                      selectedUser.userRoles.map((userRole) => (
                        <Badge
                          key={userRole.id}
                          variant={getRoleBadgeVariant(userRole.role.type)}
                        >
                          {userRole.role.name}
                        </Badge>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No roles assigned</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Information
                  </label>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="grid grid-cols-1 gap-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tenant ID:</span>
                        <code className="text-xs bg-white px-2 py-1 rounded">
                          {selectedUser.tenantId}
                        </code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Roles:</span>
                        <span className="text-gray-900">
                          {selectedUser.userRoles?.length || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button variant="outline" onClick={handleCloseUserDetail}>
                Close
              </Button>
              <PermissionGuard
                permission={{ resource: "USER", action: "WRITE" }}
              >
                <Button onClick={() => handleEditUser(selectedUser)}>
                  Edit User
                </Button>
              </PermissionGuard>
            </div>
          </div>
        )}
      </Modal>

      {/* Add User Modal */}
      <Modal
        isOpen={isAddUserModalOpen}
        onClose={handleCloseAddUser}
        title="Add User"
        size="md"
      >
        <div className="space-y-6">
          <Alert variant="info">
            <div className="space-y-3">
              <p className="font-medium">How User Management Works</p>
              <div className="text-sm space-y-2">
                <p>
                  Users must first create their own account by signing up
                  through the authentication system. Once they have an account,
                  administrators can:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Assign roles and permissions to existing users</li>
                  <li>Activate or deactivate user accounts</li>
                  <li>Manage user access within their tenant scope</li>
                </ul>
              </div>
            </div>
          </Alert>

          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Next Steps</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                  1
                </div>
                <div>
                  <p className="font-medium">Share Sign-up Link</p>
                  <p className="text-gray-600">
                    Direct new users to sign up at your application's login page
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                  2
                </div>
                <div>
                  <p className="font-medium">Assign Roles</p>
                  <p className="text-gray-600">
                    Once users appear in this list, use the "Edit" button to
                    assign appropriate roles
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                  3
                </div>
                <div>
                  <p className="font-medium">Manage Access</p>
                  <p className="text-gray-600">
                    Control user permissions and account status as needed
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={handleCloseAddUser}>
              Got It
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        isOpen={isEditUserModalOpen}
        onClose={handleCloseEditUser}
        title={
          editingUser
            ? `Edit User: ${getUserDisplayName(editingUser)}`
            : "Edit User"
        }
        size="md"
      >
        {editingUser && (
          <EditUserForm
            user={editingUser}
            onSubmit={(updateData) =>
              updateUserMutation.mutate({ userId: editingUser.id, updateData })
            }
            onCancel={handleCloseEditUser}
            isLoading={updateUserMutation.isPending}
          />
        )}
      </Modal>

      {/* Bulk Actions Modal */}
      <Modal
        isOpen={isBulkActionsModalOpen}
        onClose={handleCloseBulkActions}
        title="Bulk Actions"
        size="md"
      >
        <div className="space-y-6">
          <div>
            <p className="text-gray-600 mb-4">
              {selectedUserIds.size} user{selectedUserIds.size === 1 ? "" : "s"}{" "}
              selected
            </p>
          </div>

          <Alert variant="info">
            <div className="space-y-3">
              <p className="font-medium">Available Bulk Actions</p>
              <div className="text-sm space-y-2">
                <p>
                  Bulk actions allow you to perform operations on multiple users
                  at once. Available actions include:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Export selected users to CSV</li>
                  <li>Activate/deactivate multiple accounts</li>
                  <li>Assign roles to multiple users</li>
                  <li>Send notifications to selected users</li>
                </ul>
              </div>
            </div>
          </Alert>

          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
            <div className="grid grid-cols-1 gap-3">
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast.success("Export functionality coming soon");
                  handleCloseBulkActions();
                }}
              >
                Export Selected Users
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast.success("Bulk activation functionality coming soon");
                  handleCloseBulkActions();
                }}
              >
                Activate Selected Users
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast.success("Bulk deactivation functionality coming soon");
                  handleCloseBulkActions();
                }}
              >
                Deactivate Selected Users
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast.success(
                    "Bulk role assignment functionality coming soon",
                  );
                  handleCloseBulkActions();
                }}
              >
                Assign Roles to Selected Users
              </Button>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={handleCloseBulkActions}>
              Close
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

// Edit User Form Component
interface EditUserFormProps {
  user: User;
  onSubmit: (updateData: UpdateUserData) => void;
  onCancel: () => void;
  isLoading: boolean;
}

const EditUserForm: React.FC<EditUserFormProps> = ({
  user,
  onSubmit,
  onCancel,
  isLoading,
}) => {
  const [formData, setFormData] = useState<UpdateUserData>({
    firstName: user.firstName || "",
    lastName: user.lastName || "",
    isActive: user.isActive,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Only send changed fields
    const updateData: UpdateUserData = {};
    if (formData.firstName !== user.firstName) {
      updateData.firstName = formData.firstName || undefined;
    }
    if (formData.lastName !== user.lastName) {
      updateData.lastName = formData.lastName || undefined;
    }
    if (formData.isActive !== user.isActive) {
      updateData.isActive = formData.isActive;
    }

    // Only submit if there are changes
    if (Object.keys(updateData).length === 0) {
      toast.error("No changes to save");
      return;
    }

    onSubmit(updateData);
  };

  const handleInputChange = (
    field: keyof UpdateUserData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <FormField label="First Name">
          <Input
            type="text"
            value={formData.firstName || ""}
            onChange={(e) => handleInputChange("firstName", e.target.value)}
            placeholder="Enter first name"
          />
        </FormField>

        <FormField label="Last Name">
          <Input
            type="text"
            value={formData.lastName || ""}
            onChange={(e) => handleInputChange("lastName", e.target.value)}
            placeholder="Enter last name"
          />
        </FormField>

        <FormField label="Account Status">
          <div className="flex items-center space-x-3">
            <label className="flex items-center">
              <input
                type="radio"
                name="isActive"
                checked={formData.isActive === true}
                onChange={() => handleInputChange("isActive", true)}
                className="mr-2"
              />
              Active
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="isActive"
                checked={formData.isActive === false}
                onChange={() => handleInputChange("isActive", false)}
                className="mr-2"
              />
              Inactive
            </label>
          </div>
        </FormField>
      </div>

      <Alert variant="info">
        <div className="text-sm">
          <p className="font-medium mb-1">Note:</p>
          <p>
            Email addresses are managed through the authentication system and
            cannot be changed here. Role assignments can be managed through the
            role management interface.
          </p>
        </div>
      </Alert>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </form>
  );
};
