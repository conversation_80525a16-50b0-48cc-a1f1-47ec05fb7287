/*
  Warnings:

  - You are about to drop the `vehicle_brands_v2` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_makes` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_model_years` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_model_years_v2` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_models` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_models_v2` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_sub_brands_v2` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `vehicle_years` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "vehicle_brands_v2" DROP CONSTRAINT "vehicle_brands_v2_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_makes" DROP CONSTRAINT "vehicle_makes_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years" DROP CONSTRAINT "vehicle_model_years_modelId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years" DROP CONSTRAINT "vehicle_model_years_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years" DROP CONSTRAINT "vehicle_model_years_yearId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years_v2" DROP CONSTRAINT "vehicle_model_years_v2_modelId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years_v2" DROP CONSTRAINT "vehicle_model_years_v2_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_model_years_v2" DROP CONSTRAINT "vehicle_model_years_v2_yearId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_models" DROP CONSTRAINT "vehicle_models_makeId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_models" DROP CONSTRAINT "vehicle_models_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_models_v2" DROP CONSTRAINT "vehicle_models_v2_subBrandId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_models_v2" DROP CONSTRAINT "vehicle_models_v2_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_sub_brands_v2" DROP CONSTRAINT "vehicle_sub_brands_v2_brandId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_sub_brands_v2" DROP CONSTRAINT "vehicle_sub_brands_v2_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "vehicle_years" DROP CONSTRAINT "vehicle_years_tenantId_fkey";

-- DropTable
DROP TABLE "vehicle_brands_v2";

-- DropTable
DROP TABLE "vehicle_makes";

-- DropTable
DROP TABLE "vehicle_model_years";

-- DropTable
DROP TABLE "vehicle_model_years_v2";

-- DropTable
DROP TABLE "vehicle_models";

-- DropTable
DROP TABLE "vehicle_models_v2";

-- DropTable
DROP TABLE "vehicle_sub_brands_v2";

-- DropTable
DROP TABLE "vehicle_years";
