# AI Quick Reference - Tech Notes

This file provides essential guidance for AI coding agents working on the Tech Notes Project. For detailed patterns and examples, reference the domain-specific guides listed below.

Tech Notes is designed to help service managers distribute technical documentation to technicians in the field.  It is starting as a basic web app with (mostly manual) data management, document upload, and meta data tagging, and a mobile app for technicians to access the content.  In later phases we'll add many advanced features, but we're starting with the basics.

---

## 🔧 Project Overview

Full-stack TypeScript App (monorepo):

- **Backend**: Express + TypeScript + Prisma + PostgreSQL
- **Frontend**: React + Vite + TailwindCSS + React Query
- **Mobile**: Expo + React Native (standalone, not in shared workspace)
- **Auth**: Clerk with database-centric tenancy (invite-only system)
- **Payments**: Stripe
- **DevOps**: Render Blueprint deployment
- **Architecture**: Multi-tenant, RBAC, service-layer pattern, DI

---

## 🚨 Universal Rules

### Security & Tenancy

- **All backend operations must be tenant-scoped** unless explicitly marked global
- **Database User.tenantId is source of truth** - NOT <PERSON> metadata
- **Use RBAC middleware**: `createAuthWithPermission()` over basic `createAuth()`
- **Never hardcode secrets** - always use validated environment variables
- **Authentication flow**: Token validation → Database lookup → `req.user` context → Handler
- **Docker host binding**: Applications must bind to `0.0.0.0:PORT` (required for Render)

### Code Quality

- **TypeScript strict mode** required
- **All code must pass `yarn run check`** (lint + format + build)
- **Coverage enforcement**: 80% minimum (lines, functions, branches, statements)
- **One solution only** - implement minimal fix, stop after success
- **Ask before improving** - don't add enhancements unless requested

### Data & API

- **All models need `createdAt`/`updatedAt`** audit fields
- **Use service layer** for database operations (never raw SQL)
- **Consistent HTTP status codes** (200, 201, 400, 401, 403, 404, 500)
- **Always include request validation** (Zod preferred)

### Environment & Testing

- **Consolidated environment**: All config in root `.env.local` (not subdirectories)
- **Use direct mocking**: Use `jest.mock()` for simple, reliable tests
- **Co-locate tests**: Place `.test.ts` files next to source files

---

## 📋 Domain-Specific Guides

When working on specific areas, reference these detailed guides:

- **Authentication & Security**: See `docs/AUTHENTICATION_PATTERNS.md` _(RBAC middleware, Clerk integration, invite-only auth)_
- **Database & Tenancy**: See `docs/DATABASE_PATTERNS.md` _(BaseTenantService, withTenantScope(), migrations)_
- **Testing Strategy**: See `docs/TESTING_CONVENTIONS.md` _(direct mocking, co-located tests, simplified patterns, dependency injection)_
- **API Development**: See `docs/API_CONVENTIONS.md` _(permission-based routes, DI factories, error handling)_
- **Mobile Development**: See `docs/mobile-app.md` _(Expo standalone setup, authentication, code sharing strategy)_
- **Frontend Components**: See `docs/FRONTEND_PATTERNS.md` _(atomic design, service interfaces, dual layouts)_
- **Design System**: See `docs/DESIGN_SYSTEM.md` _(Salient-inspired tokens, component styling, cross-platform consistency)_
- **Docker & Deployment**: See `docs/DOCKER_DEPLOYMENT.md` _(multi-stage builds, Alpine compatibility, import paths)_
- **Render Deployment**: See `docs/RENDER_DEPLOYMENT.md` _(blueprint workflow, environment separation, scaling)_
- **Environment Setup**: See `docs/ENVIRONMENT_SETUP.md` _(consolidated root config, security separation)_
- **Render Troubleshooting**: See `docs/troubleshooting/render-deployment-issues.md` _(host binding, npm workspaces, migration conflicts)_

---

## 🏗️ Core Architecture Patterns

### Core Architecture

- **Frontend**: Atomic design (atoms/molecules/organisms) + centralized exports from `@/components` + Salient-inspired design system
- **Backend**: Service layer extending `BaseTenantService` + route factories + RBAC middleware
- **Authentication**: `middlewareFactory.createAuthWithPermission()` → tenant lookup → scoped operations
- **Testing**: Direct mocking with `jest.mock()` + co-located test files
- **Docker**: Host binding to `0.0.0.0:PORT` + build from project root + `./dist/generated` imports

---

## 📌 Quick Reference

### Essential RBAC Patterns

```typescript
// ✅ PREFERRED - Permission-based protection
router.get(
  "/users",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ),
  handler,
);

// ✅ PREFERRED - Multiple permissions (user must have ALL)
router.post(
  "/users",
  ...middlewareFactory.createAuthWithPermission([
    CommonPermissions.USER_CREATE,
    CommonPermissions.USER_WRITE,
  ]),
  handler,
);

// ⚠️ AVOID - Basic auth without permission checking
router.get("/users", middlewareFactory.createAuth(), handler);
```

### Essential Patterns

```typescript
// Shared workspace imports
import { formatDate, capitalize } from '@tech-notes/shared';

// RBAC-first approach
router.get('/users', ...middlewareFactory.createAuthWithPermission(CommonPermissions.USER_READ), handler);

// Direct test mocking
jest.mock('./prisma.service');
const mockPrisma = { prisma: { user: { findMany: jest.fn() } } } as any;

// AuthContext interface
interface AuthContext {
  id: string; clerkId: string; tenantId: string; email: string;
  roles?: UserRoleWithContext[]; permissions?: string[]; canBypassTenantScope?: boolean;
}

// Environment (.env.local)
DATABASE_URL="postgresql://user:password@localhost:5432/tech_notes_dev"
CLERK_SECRET_KEY="sk_test_..." VITE_CLERK_PUBLISHABLE_KEY="pk_test_..."
```

## Shared Workspace ESM/CommonJS Compatibility

**Critical**: Files imported by both runtime (ESM) and Jest (CommonJS) must avoid ESM-specific syntax.

### ❌ Avoid in Shared Code

```typescript
// ESM-specific syntax breaks Jest
import { fileURLToPath } from "url";
const __filename = fileURLToPath(import.meta.url);
```

### ✅ Use CommonJS-Compatible Patterns

```typescript
// Works in both ESM runtime and CommonJS Jest
import path from "path";
dotenv.config({ path: path.resolve(process.cwd(), ".env") });
```

### Environment Loading Pattern

- Use `process.cwd()` instead of `__dirname` for cross-compatibility
- Load environment files from project root using relative paths
- Test `yarn run check` after changes

---

**Remember**: This file is your starting point. Reference the domain-specific guides for detailed patterns, examples, and implementation specifics.
