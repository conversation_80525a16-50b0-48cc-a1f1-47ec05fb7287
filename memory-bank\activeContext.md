# Active Context - Tech Notes

## Current Work Focus

### Memory Bank Initialization (Complete)
- **Status**: ✅ Complete memory bank structure created according to .clinerules
- **Created Files**: 
  - projectbrief.md (foundation document)
  - productContext.md (why and user experience goals)
  - systemPatterns.md (architecture and technical patterns)
  - techContext.md (technologies and development setup)
  - activeContext.md (current work focus and decisions)
  - progress.md (implementation status and roadmap)

### Project Phase
Currently in **Phase 1: Foundation** - building the basic web app and mobile app infrastructure with core multi-tenant RBAC architecture.

## Recent Changes and Decisions

### Architecture Decisions Made
1. **Multi-tenant RBAC**: Strict tenant scoping with database User.tenantId as source of truth
2. **Service Layer Pattern**: All business logic in services extending BaseTenantService
3. **Permission-First Security**: Using `createAuthWithPermission()` middleware over basic auth
4. **Monorepo Structure**: Yarn workspaces for frontend/backend/shared, standalone mobile app
5. **Environment Consolidation**: Root `.env.local` instead of distributed configs

### Development Patterns Established
1. **Direct Mocking**: Jest tests use `jest.mock()` for simple, reliable testing
2. **Co-located Tests**: `.test.ts` files next to source files
3. **TypeScript Strict Mode**: Enforced across all packages
4. **Quality Gates**: `yarn run check` must pass (lint + format + build)
5. **Docker Host Binding**: Applications bind to `0.0.0.0:PORT` for Render compatibility

## Next Immediate Steps

### Memory Bank Validation
1. ✅ **All Core Files Created**: Complete hierarchical structure following .clinerules
2. ✅ **Documentation Complete**: Each file captures its designated context area
3. ✅ **Cross-references Established**: Files build upon each other as designed

### Development Priorities (Post Memory Bank)
Based on the project structure and documentation, the next development priorities would likely be:

1. **Document Management System**: Core document upload, metadata tagging, and storage
2. **Mobile App Authentication**: Clerk integration for mobile technician access
3. **Tenant Management**: User invitation and role assignment workflows
4. **Basic Search and Filtering**: Document discovery for technicians

## Active Patterns and Preferences

### Code Organization
- **Atomic Design**: Frontend components organized as atoms/molecules/organisms
- **Centralized Exports**: Components exported from `@/components` for clean imports
- **Service Factories**: Route factories that inject dependencies for testability

### Security Patterns
- **RBAC Middleware**: Always use permission-based route protection
- **Tenant Scoping**: Every database operation must be tenant-aware
- **Input Validation**: Zod schemas for all request validation
- **Environment Security**: Never hardcode secrets, always use validated env vars

### Testing Patterns
- **80% Coverage Minimum**: Lines, functions, branches, statements
- **Direct Mocking**: Avoid complex test setup, use simple jest.mock()
- **Test Co-location**: Tests live next to the code they test
- **Integration Testing**: Focus on service layer and API endpoints

## Important Project Insights

### Technical Constraints
1. **ESM/CommonJS Compatibility**: Shared code must work in both runtime (ESM) and Jest (CommonJS)
2. **Docker Host Binding**: Must bind to `0.0.0.0:PORT` for Render deployment
3. **Workspace Dependencies**: Careful management of shared package imports
4. **Database Migrations**: Automated through Render deployment pipeline

### User Experience Focus
1. **Service Manager Workflow**: Simple document upload with intuitive metadata tagging
2. **Technician Mobile Experience**: Offline-capable, fast search, reliable sync
3. **Multi-tenant Isolation**: Complete data separation between organizations
4. **Invite-only System**: Controlled access through invitation workflow

### Development Workflow
1. **Quality First**: All code must pass quality checks before deployment
2. **Incremental Development**: Build foundational features before advanced capabilities
3. **Documentation Driven**: Comprehensive docs guide development decisions
4. **Testing Integrated**: Tests written alongside feature development

## Current Technical Debt

### Known Issues
- Mobile app is standalone (not in monorepo workspace) - may need integration strategy
- S3 file storage is planned but not yet implemented
- Advanced search features are future phase items
- Usage analytics and reporting are not yet built

### Optimization Opportunities
- Database query optimization for tenant-scoped operations
- Frontend bundle optimization for mobile performance
- Caching strategies for frequently accessed documents
- Background sync optimization for mobile offline scenarios

## Decision Context

### Why These Patterns Were Chosen
1. **Tenant-first Architecture**: Ensures complete data isolation for security and compliance
2. **Permission-based Security**: Provides fine-grained access control for different user roles
3. **Service Layer Pattern**: Enables testability and separation of concerns
4. **Direct Mocking**: Simplifies test maintenance and reduces test complexity
5. **Monorepo Structure**: Enables code sharing while maintaining clear boundaries

### Trade-offs Made
1. **Complexity vs. Security**: Chose more complex RBAC for better security
2. **Performance vs. Isolation**: Tenant scoping adds queries but ensures data isolation
3. **Development Speed vs. Quality**: Quality gates slow development but prevent issues
4. **Flexibility vs. Consistency**: Strict patterns reduce flexibility but improve maintainability
