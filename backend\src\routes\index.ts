import { Router } from 'express';
import { Logger } from 'winston';

import { MiddlewareFactory } from '../middleware/middleware-factory.js';
import { DocumentService } from '../services/document.service.js';
import { InvitationService } from '../services/invitation.service.js';
import { OnboardingService } from '../services/onboarding.service.js';
import { PermissionService } from '../services/permission.service.js';
import { PrismaService } from '../services/prisma.service.js';
import { RoleService } from '../services/role.service.js';
import { S3Service } from '../services/s3.service.js';
import { TenantService } from '../services/tenant.service.js';
import { UserEngagementService } from '../services/user-engagement.service.js';
import { UserService } from '../services/user.service.js';
import { VehicleHierarchyV3CoordinatorService } from '../services/vehicle-hierarchy-v3/vehicle-hierarchy-v3-coordinator.service.js';
import { ClerkAuthMiddleware } from '../types/clerk.types.js';

import { createApiV1Routes } from './api/v1/index.js';

interface ServiceDependencies {
  prismaService: PrismaService;
  userService: UserService;
  tenantService: TenantService;
  onboardingService: OnboardingService;
  invitationService: InvitationService;
  roleService: RoleService;
  permissionService: PermissionService;
  userEngagementService: UserEngagementService;

  vehicleHierarchyV3Service: VehicleHierarchyV3CoordinatorService;
  documentService: DocumentService;
  s3Service: S3Service;
  middlewareFactory: MiddlewareFactory;
  clerkAuth: ClerkAuthMiddleware;
  logger: Logger;
}

export function createAppRoutes(dependencies: ServiceDependencies): Router {
  const router = Router();

  // Root health check for Render platform health checks
  router.get('/', (req, res) => {
    res.json({
      status: 'ok',
      service: 'tech-notes-backend',
      timestamp: new Date().toISOString(),
    });
  });

  // Handle HEAD requests to root (Render health checks)
  router.head('/', (req, res) => {
    res.status(200).end();
  });

  // Basic health check at /health for compatibility
  router.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
    });
  });

  // Mount API v1 routes at /api/v1
  router.use('/api/v1', createApiV1Routes(dependencies));

  return router;
}
