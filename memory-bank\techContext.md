# Tech Context - Tech Notes

## Technology Stack

### Backend
- **Runtime**: Node.js 18+ with TypeScript 5.8+
- **Framework**: Express.js with TypeScript strict mode
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Clerk with database-centric tenancy
- **File Storage**: AWS S3 (planned)
- **Payments**: Stripe integration
- **Testing**: Jest with direct mocking patterns

### Frontend
- **Framework**: React 18+ with TypeScript
- **Build Tool**: Vite for fast development and building
- **Styling**: TailwindCSS with Salient-inspired design system
- **State Management**: React Query for server state
- **Routing**: React Router (assumed from React app structure)
- **Testing**: Vitest for unit testing

### Mobile
- **Framework**: Expo React Native (standalone, not in monorepo workspace)
- **Platform**: Cross-platform iOS/Android
- **Authentication**: Clerk integration
- **Offline Storage**: SQLite for local data
- **Synchronization**: Custom sync logic with backend API

### Shared Infrastructure
- **Monorepo**: Yarn workspaces for frontend/backend/shared
- **Package Manager**: Yarn (migrated from npm)
- **Linting**: ESLint with TypeScript support
- **Formatting**: Prettier
- **Type Checking**: TypeScript strict mode across all packages

## Development Environment Setup

### Workspace Configuration

**Monorepo Structure**:
```json
{
  "workspaces": ["frontend", "backend", "shared"],
  "scripts": {
    "dev": "concurrently backend and frontend development servers",
    "build": "yarn workspaces run build",
    "test": "yarn workspaces run test",
    "check": "yarn workspaces run check"
  }
}
```

**Environment Management**:
- **Consolidated Config**: All environment variables in root `.env.local`
- **No Subdirectory Configs**: Avoid `.env` files in workspace subdirectories
- **Security Separation**: Separate configs for development, test, and production

### Database Setup

**Development Database**:
```bash
# Database preparation script
yarn run db:prepare
# Equivalent to:
# - docker-compose up -d (PostgreSQL container)
# - yarn run db:migrate (Prisma migrations)
# - yarn run db:generate (Prisma client generation)
# - yarn run db:seed (Base data seeding)
# - yarn run seed:dev:roles (Development role setup)
```

**Database Tools**:
- **Prisma Studio**: `yarn run db:studio` for database GUI
- **Migrations**: Prisma migrate for schema changes
- **Seeding**: Automated seeding for development and testing

### Development Scripts

**Core Commands**:
```bash
yarn dev              # Start both backend and frontend
yarn build            # Build all workspaces
yarn test             # Run all tests
yarn check            # Lint + format + build verification
yarn clean            # Clean all build artifacts
```

**Database Commands**:
```bash
yarn run db:setup     # Full database setup + studio
yarn run db:studio    # Open Prisma Studio
yarn run db:prepare   # Setup database for development
```

## Technical Constraints

### Docker and Deployment

**Host Binding Requirements**:
- Applications must bind to `0.0.0.0:PORT` (required for Render deployment)
- Cannot use `localhost` or `127.0.0.1` in production builds
- Docker containers must expose ports correctly for external access

**Build Context**:
- Docker builds from project root
- Import paths use `./dist/generated` for built artifacts
- Multi-stage builds for production optimization

### Render Deployment

**Blueprint Configuration**:
- Automated deployment through render.yaml
- Environment variable management through Render dashboard
- Database migrations run automatically on deployment
- Static file serving for frontend builds

**Scaling Considerations**:
- Horizontal scaling support for backend services
- Database connection pooling for concurrent requests
- CDN integration for static assets

### ESM/CommonJS Compatibility

**Critical Constraint**: Files imported by both runtime (ESM) and Jest (CommonJS) must avoid ESM-specific syntax.

**Avoid in Shared Code**:
```typescript
// ❌ ESM-specific syntax breaks Jest
import { fileURLToPath } from "url";
const __filename = fileURLToPath(import.meta.url);
```

**Use CommonJS-Compatible Patterns**:
```typescript
// ✅ Works in both ESM runtime and CommonJS Jest
import path from "path";
dotenv.config({ path: path.resolve(process.cwd(), ".env") });
```

## Dependencies and Package Management

### Shared Workspace Dependencies

**@tech-notes/shared Package**:
- Common utilities and types
- Shared validation schemas (Zod)
- Cross-platform compatible code
- Build target: Both ESM and CommonJS

**Import Patterns**:
```typescript
// Shared workspace imports
import { formatDate, capitalize } from '@tech-notes/shared';
import { UserRole, DocumentType } from '@tech-notes/shared/types';
```

### Version Management

**Node.js Requirements**:
- Minimum Node.js 18.0.0
- Minimum npm 9.0.0 (though using Yarn)
- TypeScript 5.8+ for latest features

**Dependency Strategy**:
- Lock file management with yarn.lock
- Workspace dependency hoisting
- Peer dependency management for shared packages

## Testing Infrastructure

### Testing Philosophy

**Direct Mocking Approach**:
```typescript
// ✅ PREFERRED - Direct mocking
jest.mock('./prisma.service');
const mockPrisma = { prisma: { user: { findMany: jest.fn() } } } as any;
```

**Co-located Tests**:
- Place `.test.ts` files next to source files
- Test files mirror source structure
- Easy navigation between source and tests

### Test Configuration

**Jest Configuration**:
- TypeScript support with ts-jest
- Module path mapping for imports
- Coverage thresholds: 80% minimum
- Test environment setup for database mocking

**Coverage Requirements**:
- Lines: 80% minimum
- Functions: 80% minimum
- Branches: 80% minimum
- Statements: 80% minimum

## Code Quality Standards

### TypeScript Configuration

**Strict Mode Requirements**:
- `strict: true` in all tsconfig.json files
- No implicit any types
- Strict null checks enabled
- Unused locals and parameters flagged

### Linting and Formatting

**ESLint Configuration**:
- TypeScript-specific rules
- React hooks rules for frontend
- Custom local rules for project-specific patterns
- Import/export validation

**Prettier Configuration**:
- Consistent code formatting
- Automatic formatting on save
- Integration with ESLint for conflict resolution

### Build and Quality Checks

**Quality Gate Requirements**:
- All code must pass `yarn run check` before deployment
- Includes: linting, formatting, type checking, and building
- No warnings or errors allowed in production builds

## Environment Variables

### Required Environment Variables

**Database**:
```bash
DATABASE_URL="postgresql://user:password@localhost:5432/tech_notes_dev"
```

**Authentication (Clerk)**:
```bash
CLERK_SECRET_KEY="sk_test_..."
VITE_CLERK_PUBLISHABLE_KEY="pk_test_..."
```

**File Storage (S3)**:
```bash
AWS_ACCESS_KEY_ID="..."
AWS_SECRET_ACCESS_KEY="..."
AWS_S3_BUCKET_NAME="..."
AWS_REGION="..."
```

**Payments (Stripe)**:
```bash
STRIPE_SECRET_KEY="sk_test_..."
VITE_STRIPE_PUBLISHABLE_KEY="pk_test_..."
```

### Environment Loading Pattern

**Root Configuration**:
- All environment variables in root `.env.local`
- Use `process.cwd()` for cross-compatibility
- Load from project root using relative paths

```typescript
// Environment loading pattern
import path from "path";
import dotenv from "dotenv";

dotenv.config({ path: path.resolve(process.cwd(), ".env.local") });
```

## Tool Usage Patterns

### Development Tools

**Package Management**:
- Use `yarn` for all package operations
- Workspace commands: `yarn workspace <name> <command>`
- Global commands: `yarn run <script>` from root

**Database Management**:
- Prisma Studio for visual database management
- Migration workflow: create → apply → generate client
- Seeding for consistent development data

**Code Quality**:
- Pre-commit hooks for quality checks
- Automated formatting on save
- Continuous integration quality gates
