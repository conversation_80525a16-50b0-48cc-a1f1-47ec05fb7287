# System Patterns - Tech Notes

## Architecture Overview

Tech Notes follows a multi-tenant, service-oriented architecture with strict security boundaries and role-based access control. The system is designed as a monorepo with shared components and clear separation of concerns.

## Core Architectural Patterns

### Multi-Tenant Architecture

**Tenant Scoping Strategy**:
- Database `User.tenantId` is the source of truth (NOT Clerk metadata)
- All backend operations must be tenant-scoped unless explicitly marked global
- Service layer enforces tenant boundaries through `BaseTenantService`
- Authentication flow: Token validation → Database lookup → `req.user` context → Handler

**Key Components**:
```typescript
// BaseTenantService pattern
class DocumentService extends BaseTenantService {
  async findMany(tenantId: string, filters?: DocumentFilters) {
    return this.withTenantScope(tenantId, () => {
      return this.prisma.document.findMany({
        where: { tenantId, ...filters }
      });
    });
  }
}
```

### Role-Based Access Control (RBAC)

**Permission-First Approach**:
- Use `createAuthWithPermission()` over basic `createAuth()`
- Middleware validates permissions before handler execution
- Support for multiple permission requirements (user must have ALL)

**RBAC Middleware Pattern**:
```typescript
// ✅ PREFERRED - Permission-based protection
router.get(
  "/documents",
  ...middlewareFactory.createAuthWithPermission(CommonPermissions.DOCUMENT_READ),
  handler,
);

// ✅ Multiple permissions
router.post(
  "/documents",
  ...middlewareFactory.createAuthWithPermission([
    CommonPermissions.DOCUMENT_CREATE,
    CommonPermissions.DOCUMENT_WRITE,
  ]),
  handler,
);
```

### Service Layer Pattern

**Dependency Injection Architecture**:
- Services extend `BaseTenantService` for consistent tenant scoping
- Route factories inject dependencies for testability
- Clear separation between routes, services, and data access

**Service Structure**:
```typescript
// Service registration
const documentService = new DocumentService(prisma);
const documentRoutes = createDocumentRoutes(documentService);

// Route factory pattern
export function createDocumentRoutes(documentService: DocumentService) {
  const router = express.Router();
  // Route definitions with injected service
  return router;
}
```

## Component Relationships

### Monorepo Structure

```
tech-notes/
├── frontend/          # React + Vite + TailwindCSS
├── backend/           # Express + TypeScript + Prisma
├── shared/            # Common utilities and types
├── mobile/            # Expo React Native (standalone)
└── infrastructure/    # Docker and deployment configs
```

### Frontend Architecture

**Atomic Design Pattern**:
- **Atoms**: Basic UI elements (buttons, inputs, icons)
- **Molecules**: Simple component combinations (form fields, cards)
- **Organisms**: Complex UI sections (headers, forms, lists)
- **Templates**: Page layouts and structure
- **Pages**: Complete views with data integration

**Component Export Strategy**:
```typescript
// Centralized exports from @/components
export { Button } from './atoms/Button';
export { FormField } from './molecules/FormField';
export { DocumentList } from './organisms/DocumentList';
```

### Backend Architecture

**Layered Service Architecture**:
```
Routes (HTTP) → Middleware (Auth/RBAC) → Services (Business Logic) → Prisma (Data Access)
```

**Key Patterns**:
- Route handlers are thin, delegating to services
- Services contain business logic and tenant scoping
- Middleware handles cross-cutting concerns (auth, validation, logging)
- Prisma provides type-safe database access

## Critical Implementation Paths

### Authentication Flow

1. **Token Validation**: Clerk JWT validation in middleware
2. **Database Lookup**: Fetch user record with tenant and role information
3. **Context Creation**: Build `AuthContext` with permissions
4. **Request Scoping**: Attach context to `req.user` for handlers
5. **Permission Checking**: Validate required permissions before execution

### Document Management Flow

1. **Upload**: File storage (S3) + metadata extraction
2. **Processing**: Metadata tagging and categorization
3. **Distribution**: Tenant-scoped access control
4. **Synchronization**: Mobile app sync with offline support

### Mobile Synchronization

1. **Authentication**: Clerk token exchange
2. **Data Sync**: Incremental updates based on last sync timestamp
3. **Offline Storage**: Local SQLite with conflict resolution
4. **Background Sync**: Automatic updates when connectivity available

## Design System Integration

**Salient-Inspired Design Tokens**:
- Consistent color palette across web and mobile
- Typography scale with semantic naming
- Component styling with design system tokens
- Cross-platform consistency through shared design language

## Security Patterns

### Input Validation
- Zod schemas for request validation
- Type-safe parameter parsing
- Sanitization of user inputs

### Data Protection
- Environment variable validation
- Secrets management through secure environment loading
- Database connection security with connection pooling

### Tenant Isolation
- Strict tenant scoping in all database queries
- Row-level security considerations
- Audit trails for tenant data access

## Performance Patterns

### Database Optimization
- Efficient indexing strategy for tenant-scoped queries
- Connection pooling for concurrent requests
- Query optimization with Prisma query analysis

### Frontend Performance
- Code splitting with React.lazy
- Optimistic updates with React Query
- Efficient re-rendering with proper dependency arrays

### Mobile Performance
- Offline-first data architecture
- Efficient synchronization strategies
- Background processing for non-critical updates
